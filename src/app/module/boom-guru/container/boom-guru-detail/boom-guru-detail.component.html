<div class="px-3 py-4">
  <div class="h4 py-2 mb-3 text-center nav-back">
    <i class="icon icon-back mr-2 float-left" (click)="goBack()"></i>
    {{ "_boom_guru_detail" | translate }}
  </div>

  <div *ngIf="loading" class="text-center py-5">
    <div class="spinner-border" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>

  <div *ngIf="error && !loading" class="alert alert-danger">
    {{ error }}
  </div>

  <div *ngIf="detail && !loading" class="boom-guru-detail">
    <!-- Image Section -->
    <div class="image-container text-center mb-2">
      <img [src]="detail.imageUrl" class="img-fluid rounded" alt="Guru Image">
    </div>

    <!-- Question Section -->
    <div class="question-container text-center  mb-3">
      <div class="question-text text-muted ">
        {{ detail.questionDescription }}
      </div>
      <div class="text-muted mt-1">
        <span class="font-weight-bold" *ngIf="detail.serialNumber">
          {{ detail.serialNumber | serialFormat }}
        </span>
        {{ detail.requestDate | date:'dd.MM.yyyy HH:mm' }}
      </div>
      <div class="question-text text-muted ">
        {{ '_boom_guru_disclaimer' |translate }}
      </div>
    </div>

    <!-- Answer Section -->
    <div class="answer-section">
      <div class="answer-title mb-3">
        {{ "_guru_answer" | translate }}:
      </div>
      <div class="chat-bubble">
        <div class="chat-text" [innerHTML]="guruAnswerHtml | safeHtml">
        </div>
      </div>
    </div>

    <!-- Feedback Section -->
    <div class="feedback-section mt-4" *ngIf="!feedbackSubmitted">
      <div class="feedback-question text-center mb-3">
        <h6>{{ "_feedback_question" | translate }}</h6>
      </div>
      <div class="feedback-options d-flex justify-content-center">
        <div class="feedback-option mx-2"
             [class.selected]="selectedFeedback === 1"
             [class.loading]="feedbackLoading && selectedFeedback === 1"
             (click)="submitFeedback(1)">
          <div class="feedback-icon">
            <i class="fas fa-thumbs-up" [class.text-success]="selectedFeedback === 1"></i>
          </div>
          <div class="feedback-label">{{ "_feedback_yes" | translate }}</div>
        </div>

        <div class="feedback-option mx-2"
             [class.selected]="selectedFeedback === 3"
             [class.loading]="feedbackLoading && selectedFeedback === 3"
             (click)="submitFeedback(3)">
          <div class="feedback-icon">
            <i class="icon icon-back" [class.text-warning]="selectedFeedback === 3"></i>
          </div>
          <div class="feedback-label">{{ "_feedback_neutral" | translate }}</div>
        </div>

        <div class="feedback-option mx-2"
             [class.selected]="selectedFeedback === 2"
             [class.loading]="feedbackLoading && selectedFeedback === 2"
             (click)="submitFeedback(2)">
          <div class="feedback-icon">
            <i class="fas fa-thumbs-down" [class.text-danger]="selectedFeedback === 2"></i>
          </div>
          <div class="feedback-label">{{ "_feedback_no" | translate }}</div>
        </div>
      </div>
    </div>

    <!-- Feedback Success Message -->
    <div class="feedback-success text-center mt-4" *ngIf="feedbackSubmitted">
      <div class="alert alert-success">
        <i class="fas fa-check-circle mr-2"></i>
        {{ "_feedback_success" | translate }}
      </div>
    </div>
  </div>
</div>
