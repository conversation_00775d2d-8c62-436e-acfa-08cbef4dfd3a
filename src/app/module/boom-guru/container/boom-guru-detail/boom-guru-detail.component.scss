.boom-guru-detail {
  max-width: 800px;
  margin: 0 auto;
}

.image-container {
  img {
    max-height: 400px;
    object-fit: contain;
  }
}

.question-container {
  .question-text {
    font-size: 13px;
    font-weight: 500;
    color: #333;
  }
}

.answer-section {
  .answer-title {
    font-size: 15px;
    font-weight: 500;
    color: #555;
  }
}

.chat-bubble {
  background-color: #f1f0f0;
  border-radius: 18px;
  padding: 12px 18px;
  max-width: 100%;
  margin-bottom: 10px;
  position: relative;

  .chat-text {
    font-size: 16px;
    color: #333;
    line-height: 1.5;

    // Markdown styling
    h1, h2, h3 {
      margin-top: 0.5em;
      margin-bottom: 0.5em;
      font-weight: 600;
    }

    h1 {
      font-size: 1.5em;
    }

    h2 {
      font-size: 1.3em;
    }

    h3 {
      font-size: 1.1em;
    }

    p {
      margin-bottom: 0.75em;
    }

    ul {
      padding-left: 20px;
      margin-bottom: 0.75em;
    }

    li {
      margin-bottom: 0.25em;
    }

    a {
      color: #007bff;
      text-decoration: underline;
    }

    strong {
      font-weight: 600;
    }

    em {
      font-style: italic;
    }
  }

  &:after {
    content: '';
    position: absolute;
    left: -10px;
    top: 15px;
    width: 0;
    height: 0;
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 10px solid #f1f0f0;
  }
}

.feedback-section {
  border-top: 1px solid #e0e0e0;
  padding-top: 20px;
  margin-top: 20px;

  .feedback-question {
    h6 {
      color: #555;
      font-weight: 500;
      margin-bottom: 0;
    }
  }

  .feedback-options {
    gap: 15px;
  }

  .feedback-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fff;
    min-width: 80px;

    &:hover {
      border-color: #007bff;
      background-color: #f8f9fa;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      border-color: #007bff;
      background-color: #e3f2fd;
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 123, 255, 0.2);
    }

    &.loading {
      opacity: 0.7;
      cursor: not-allowed;

      .feedback-icon i {
        animation: pulse 1.5s infinite;
      }
    }

    .feedback-icon {
      font-size: 24px;
      margin-bottom: 8px;
      color: #666;

      i {
        transition: color 0.3s ease;
      }
    }

    .feedback-label {
      font-size: 12px;
      font-weight: 500;
      color: #666;
      text-align: center;
    }

    &:hover .feedback-icon i,
    &.selected .feedback-icon i {
      color: #007bff;
    }

    &.selected .feedback-label {
      color: #007bff;
    }
  }
}

.feedback-success {
  .alert {
    border: none;
    border-radius: 12px;
    font-weight: 500;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
