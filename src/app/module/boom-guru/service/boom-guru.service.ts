import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpResponse } from 'src/app/response/http.response';
import { environment } from 'src/environments/environment';
import { BoomGuruDetailModel, BoomGuruItemModel, BoomGuruResponse, BoomGuruFeedbackRequest, BoomGuruFeedbackResponse } from '../model/boom-guru.model';

@Injectable({
  providedIn: 'root'
})
export class BoomGuruService {

  constructor(
    private readonly http: HttpClient,
  ) { }

  getItems(): Observable<BoomGuruResponse> {
    // For testing purposes, you can use the mock data below
    // return of({
    //   "guruResponses": [
    //     {
    //       "GuruRequestId": "d3b07384-d9a0-4c25-8e5b-4b2c3f1a2b3c",
    //       "ImageUrl": "http://localhost:2287/api/image/equipment?productH…261401&model=336D2L&imageSize=mobilethumbnailsize",
    //       "ThumbnailUrl": "http://localhost:2287/api/image/equipment?productH…261401&model=336D2L&imageSize=mobilefullsize",
    //       "SerialNumber": "SN123456789",
    //       "RequestDate": "2023-10-01T14:30:00Z",
    //       "IsInspectionForm": true
    //     },
    //     {
    //       "GuruRequestId": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
    //       "ImageUrl": "http://localhost:2287/api/image/equipment?productH…261401&model=336D2L&imageSize=mobilethumbnailsize",
    //       "ThumbnailUrl": "http://localhost:2287/api/image/equipment?productH…261401&model=336D2L&imageSize=mobilethumbnailsize",
    //       "SerialNumber": "SN987654321",
    //       "RequestDate": "2023-10-02T10:15:00Z",
    //       "IsInspectionForm": false
    //     }
    //   ],
    //   "totalCount": 2
    // });

    return this.http.get<HttpResponse<BoomGuruResponse>>(`${environment.api}/guru/list`)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return { guruResponses: [], totalCount: 0 };
        }),
      );
  }

  submitImage(image: File, serialNumber: string): Observable<any> {
    const formData = new FormData();
    formData.append('image', image);
    formData.append('SerialNumber', serialNumber);

    return this.http.post<HttpResponse<any>>(`${environment.api}/guru/submit`, formData)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          throw new Error(val.message || 'An error occurred');
        }),
      );
  }

  getDetail(guruRequestId: string): Observable<BoomGuruDetailModel> {
    // For testing purposes, you can use the mock data below
    // return of({
    //   "guruRequestId": guruRequestId,
    //   "customerId": "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
    //   "imageUrl": "http://localhost:2287/api/image/equipment?productH…261401&model=336D2L&imageSize=mobilefullsize",
    //   "thumbnailUrl": "http://localhost:2287/api/image/equipment?productH…261401&model=336D2L&imageSize=mobilefullsize",
    //   "serialNumber": "SN123456789",
    //   "requestDate": "2023-10-01T14:30:00Z",
    //   "questionDescription": "What is the best way to implement a Blazor component?",
    //   "guruAnswer": "The best way is to use reusable components with proper state management. When creating Blazor components, focus on making them modular and reusable. Implement proper state management using parameters, cascading values, or a state container pattern depending on your needs. Make sure to handle component lifecycle methods appropriately and optimize rendering performance by implementing ShouldRender() when necessary. Also, consider using CSS isolation for component-specific styles."
    // });

    const payload = {
      GuruRequestId: guruRequestId
    };

    return this.http.post<HttpResponse<BoomGuruDetailModel>>(`${environment.api}/guru/detail`, payload)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          throw new Error(val.message || 'An error occurred');
        }),
      );
  }
}
